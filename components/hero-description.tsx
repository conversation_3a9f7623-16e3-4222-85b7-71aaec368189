'use client'

import { motion } from 'framer-motion'
import { useScrollAnimation } from '@/hooks/use-scroll-animation'

export default function HeroDescription() {
  const { ref, isVisible } = useScrollAnimation(0.3)

  return (
    <div className='w-full bg-gradient-to-r from-rose-600 via-rose-700 to-rose-800 dark:from-rose-800 dark:via-rose-900 dark:to-rose-950 py-16 relative overflow-hidden'>
      {/* Background decoration */}
      <div className='absolute inset-0 opacity-10'>
        <svg className='w-full h-full' viewBox='0 0 100 100' preserveAspectRatio='none'>
          <path d='M0,20 Q50,5 100,20 L100,80 Q50,95 0,80 Z' fill='currentColor' className='text-white' />
        </svg>
      </div>

      <div className='container mx-auto px-4 relative z-10'>
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <motion.p
            initial={{ opacity: 0 }}
            animate={isVisible ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
            className='text-white font-archivo-narrow text-base md:text-lg leading-relaxed text-center max-w-4xl mx-auto tracking-wide'
          >
            SUN & SAGEBRUSH CREATIVE IS A SMALL WEB DEVELOPMENT AND CREATIVE
            AGENCY BASED IN JACKSON HOLE, WY. AT SUN & SAGEBRUSH CREATIVE, WE
            VALUE OUR TIME SPENT OUTSIDE AND FEEL A DEEP CONNECTION TO THE WILD
            SPACES AROUND US AND WISH TO FEEL THAT REFLECTED IN OUR WORK. IT'S
            ABOUT THE COMMUNITY THAT IS BUILT AROUND WILD SPACES, AND THE JOURNEY,
            AWE, AND MYSTERY LIFE PROVIDES US WHEN WE KNOW THEY EXIST
          </motion.p>

          {/* Decorative elements */}
          <motion.div
            initial={{ scale: 0 }}
            animate={isVisible ? { scale: 1 } : { scale: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className='flex justify-center mt-8 space-x-4'
          >
            <div className='w-2 h-2 bg-white rounded-full animate-float' style={{ animationDelay: '0s' }} />
            <div className='w-2 h-2 bg-white rounded-full animate-float' style={{ animationDelay: '0.5s' }} />
            <div className='w-2 h-2 bg-white rounded-full animate-float' style={{ animationDelay: '1s' }} />
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
